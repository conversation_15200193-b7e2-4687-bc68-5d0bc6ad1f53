#!/usr/bin/env node

/**
 * Main Migration Script
 * Orchestrates the complete data migration from JSON files to database
 */

const fs = require('fs');
const path = require('path');
const { PrismaClient } = require('@prisma/client');
const bcrypt = require('bcryptjs');

// Import our migration utilities
const {
  IdMappingManager,
  PhoneNormalizer,
  EmailGenerator,
  CourseCreator,
  LocationParser,
  ScheduleConverter
} = require('./id-mapping-strategy');

// Initialize Prisma client
const prisma = new PrismaClient();

// File paths
const DATA_DIR = path.join(process.cwd(), 'database seed data');
const CABINET_FILE = path.join(DATA_DIR, 'Cabinet (1).json');
const TEACHER_FILE = path.join(DATA_DIR, 'Teacher (1).json');
const CLASS_FILE = path.join(DATA_DIR, 'Class (1).json');

// Migration state
const migration = {
  dryRun: process.argv.includes('--dry-run'),
  verbose: process.argv.includes('--verbose'),
  startTime: new Date(),
  stats: {
    cabinets: { total: 0, success: 0, errors: 0 },
    teachers: { total: 0, success: 0, errors: 0 },
    courses: { total: 0, success: 0, errors: 0 },
    groups: { total: 0, success: 0, errors: 0 }
  },
  errors: [],
  mappings: new IdMappingManager()
};

// Utility instances
const phoneNormalizer = new PhoneNormalizer();
const emailGenerator = new EmailGenerator();
const courseCreator = new CourseCreator();
const locationParser = new LocationParser();
const scheduleConverter = new ScheduleConverter();

/**
 * Load JSON file safely
 */
function loadJsonFile(filePath) {
  try {
    if (!fs.existsSync(filePath)) {
      throw new Error(`File not found: ${filePath}`);
    }
    const content = fs.readFileSync(filePath, 'utf8');
    return JSON.parse(content);
  } catch (error) {
    console.error(`❌ Error loading ${filePath}:`, error.message);
    return [];
  }
}

/**
 * Log migration event
 */
async function logMigration(entityType, oldId, newId, status, errorMessage = null, metadata = null) {
  if (migration.dryRun) {
    console.log(`[DRY RUN] Log: ${entityType} ${oldId} → ${newId} (${status})`);
    return;
  }

  try {
    await prisma.migrationLog.create({
      data: {
        entityType,
        oldId,
        newId: newId || 'N/A',
        status,
        errorMessage,
        metadata
      }
    });
  } catch (error) {
    console.error('Failed to log migration:', error.message);
  }
}

/**
 * Migrate Cabinet data
 */
async function migrateCabinets(cabinets) {
  console.log('\n📋 Migrating Cabinets...');
  migration.stats.cabinets.total = cabinets.length;

  for (const cabinet of cabinets) {
    try {
      const { floor, building } = locationParser.parse(cabinet.location);
      const newId = migration.mappings.createMapping('cabinets', cabinet.id);

      const cabinetData = {
        id: newId,
        name: cabinet.name,
        number: cabinet.name, // Use name as number
        capacity: cabinet.capacity,
        floor: floor,
        building: building,
        branch: 'main',
        equipment: JSON.stringify(cabinet.equipment || []),
        isActive: cabinet.status === 'available',
        notes: `Migrated from: ${cabinet.location}`,
      };

      if (migration.verbose) {
        console.log(`   Processing cabinet: ${cabinet.name} (${cabinet.location})`);
      }

      if (!migration.dryRun) {
        await prisma.cabinet.create({ data: cabinetData });
      }

      await logMigration('cabinet', cabinet.id, newId, 'success', null, {
        originalLocation: cabinet.location,
        parsedFloor: floor,
        parsedBuilding: building
      });

      migration.stats.cabinets.success++;

    } catch (error) {
      console.error(`❌ Failed to migrate cabinet ${cabinet.name}:`, error.message);
      migration.errors.push(`Cabinet ${cabinet.name}: ${error.message}`);
      migration.stats.cabinets.errors++;
      
      await logMigration('cabinet', cabinet.id, null, 'error', error.message);
    }
  }

  console.log(`   ✅ Cabinets: ${migration.stats.cabinets.success}/${migration.stats.cabinets.total} successful`);
}

/**
 * Migrate Teacher data (Users + Teacher profiles)
 */
async function migrateTeachers(teachers) {
  console.log('\n👨‍🏫 Migrating Teachers...');
  migration.stats.teachers.total = teachers.length;

  for (const teacher of teachers) {
    try {
      // Generate unique email and normalize phone
      const email = teacher.email && teacher.email.includes('@') && !teacher.email.match(/^email@\d+$/) 
        ? teacher.email 
        : emailGenerator.generateFromName(teacher.name);
      
      const phone = phoneNormalizer.normalize(teacher.phone);
      
      // Create new IDs
      const userId = migration.mappings.createMapping('users', `user_${teacher.id}`);
      const teacherId = migration.mappings.createMapping('teachers', teacher.id);
      
      // Store user-teacher relationship
      migration.mappings.mappings.users.set(teacherId, userId);

      if (migration.verbose) {
        console.log(`   Processing teacher: ${teacher.name} (${email})`);
      }

      // Create User account
      const userData = {
        id: userId,
        name: teacher.name,
        email: email,
        phone: phone,
        role: 'TEACHER',
        branch: 'main',
        password: await bcrypt.hash('teacher123', 10), // Default password
      };

      // Create Teacher profile
      const teacherData = {
        id: teacherId,
        userId: userId,
        subject: Array.isArray(teacher.subjects) ? teacher.subjects[0] : teacher.subject || 'English',
        experience: 5, // Default experience
        salary: 5000000, // Default salary
        branch: 'main',
        qualifications: JSON.stringify(teacher.qualifications || []),
        notes: `Migrated teacher. Original join date: ${teacher.joinDate}`,
        originalId: teacher.id,
      };

      if (!migration.dryRun) {
        // Create user first, then teacher profile
        await prisma.user.create({ data: userData });
        await prisma.teacher.create({ data: teacherData });
      }

      await logMigration('teacher', teacher.id, teacherId, 'success', null, {
        originalEmail: teacher.email,
        generatedEmail: email,
        originalPhone: teacher.phone,
        normalizedPhone: phone,
        subjects: teacher.subjects,
        qualifications: teacher.qualifications
      });

      migration.stats.teachers.success++;

    } catch (error) {
      console.error(`❌ Failed to migrate teacher ${teacher.name}:`, error.message);
      migration.errors.push(`Teacher ${teacher.name}: ${error.message}`);
      migration.stats.teachers.errors++;
      
      await logMigration('teacher', teacher.id, null, 'error', error.message);
    }
  }

  console.log(`   ✅ Teachers: ${migration.stats.teachers.success}/${migration.stats.teachers.total} successful`);
}

/**
 * Create Course records from class analysis
 */
async function createCourses(classes) {
  console.log('\n📚 Creating Courses...');
  
  // Analyze unique course combinations
  const courseMap = new Map();
  
  classes.forEach(classItem => {
    if (classItem.level && classItem.subject && classItem.courseAmount) {
      const courseKey = `${classItem.level}-${classItem.subject}-${classItem.courseAmount}`;
      if (!courseMap.has(courseKey)) {
        const course = courseCreator.createCourse(classItem.level, classItem.subject, classItem.courseAmount);
        courseMap.set(courseKey, course);
      }
    }
  });

  migration.stats.courses.total = courseMap.size;

  for (const [courseKey, courseData] of courseMap) {
    try {
      const courseId = migration.mappings.createMapping('courses', courseKey);
      
      const dbCourseData = {
        id: courseId,
        name: courseData.name,
        level: courseData.level,
        description: courseData.description,
        duration: courseData.duration,
        price: courseData.price,
        isActive: courseData.isActive,
      };

      if (migration.verbose) {
        console.log(`   Creating course: ${courseData.name} (${courseData.level})`);
      }

      if (!migration.dryRun) {
        await prisma.course.create({ data: dbCourseData });
      }

      await logMigration('course', courseKey, courseId, 'success', null, courseData);
      migration.stats.courses.success++;

    } catch (error) {
      console.error(`❌ Failed to create course ${courseKey}:`, error.message);
      migration.errors.push(`Course ${courseKey}: ${error.message}`);
      migration.stats.courses.errors++;
      
      await logMigration('course', courseKey, null, 'error', error.message);
    }
  }

  console.log(`   ✅ Courses: ${migration.stats.courses.success}/${migration.stats.courses.total} successful`);
}

/**
 * Migrate Class data as Groups
 */
async function migrateGroups(classes) {
  console.log('\n🎓 Migrating Groups...');
  migration.stats.groups.total = classes.length;

  for (const classItem of classes) {
    try {
      // Map foreign keys
      const teacherId = migration.mappings.getNewId('teachers', classItem.teacherId);
      const cabinetId = migration.mappings.getNewId('cabinets', classItem.cabinetId);
      
      // Generate course key and get course ID
      const courseKey = `${classItem.level}-${classItem.subject}-${classItem.courseAmount}`;
      const courseId = migration.mappings.getNewId('courses', courseKey);
      
      if (!teacherId) {
        throw new Error(`Teacher not found: ${classItem.teacherId}`);
      }
      if (!courseId) {
        throw new Error(`Course not found: ${courseKey}`);
      }

      const groupId = migration.mappings.createMapping('groups', classItem.id);
      
      // Calculate end date (start date + course duration weeks)
      const startDate = new Date(classItem.openingDate || classItem.createdAt);
      const endDate = new Date(startDate);
      const course = await prisma.course.findUnique({ where: { id: courseId } });
      endDate.setDate(startDate.getDate() + (course?.duration || 12) * 7);

      const groupData = {
        id: groupId,
        name: classItem.name,
        courseId: courseId,
        teacherId: teacherId,
        capacity: 20, // Default capacity
        schedule: scheduleConverter.convert(classItem.schedule),
        room: classItem.name, // Use class name as room identifier
        cabinetId: cabinetId,
        branch: 'main',
        startDate: startDate,
        endDate: endDate,
        isActive: true,
        stage: classItem.stage,
        language: classItem.language,
        originalId: classItem.id,
      };

      if (migration.verbose) {
        console.log(`   Processing group: ${classItem.name}`);
      }

      if (!migration.dryRun) {
        await prisma.group.create({ data: groupData });
      }

      await logMigration('group', classItem.id, groupId, 'success', null, {
        originalTeacherId: classItem.teacherId,
        originalCabinetId: classItem.cabinetId,
        courseKey: courseKey,
        stage: classItem.stage,
        language: classItem.language
      });

      migration.stats.groups.success++;

    } catch (error) {
      console.error(`❌ Failed to migrate group ${classItem.name}:`, error.message);
      migration.errors.push(`Group ${classItem.name}: ${error.message}`);
      migration.stats.groups.errors++;
      
      await logMigration('group', classItem.id, null, 'error', error.message);
    }
  }

  console.log(`   ✅ Groups: ${migration.stats.groups.success}/${migration.stats.groups.total} successful`);
}

/**
 * Main migration function
 */
async function runMigration() {
  console.log('🚀 Starting Data Migration...');
  console.log(`Mode: ${migration.dryRun ? 'DRY RUN' : 'LIVE MIGRATION'}`);
  console.log(`Verbose: ${migration.verbose ? 'ON' : 'OFF'}`);
  
  try {
    // Load data files
    console.log('\n📂 Loading data files...');
    const cabinets = loadJsonFile(CABINET_FILE);
    const teachers = loadJsonFile(TEACHER_FILE);
    const classes = loadJsonFile(CLASS_FILE);
    
    console.log(`   Loaded: ${cabinets.length} cabinets, ${teachers.length} teachers, ${classes.length} classes`);

    // Run migrations in order
    await migrateCabinets(cabinets);
    await migrateTeachers(teachers);
    await createCourses(classes);
    await migrateGroups(classes);

    // Generate final report
    const duration = (new Date() - migration.startTime) / 1000;
    const totalRecords = migration.stats.cabinets.total + migration.stats.teachers.total + 
                        migration.stats.courses.total + migration.stats.groups.total;
    const totalSuccess = migration.stats.cabinets.success + migration.stats.teachers.success + 
                        migration.stats.courses.success + migration.stats.groups.success;
    const totalErrors = migration.stats.cabinets.errors + migration.stats.teachers.errors + 
                       migration.stats.courses.errors + migration.stats.groups.errors;

    console.log('\n📊 Migration Summary:');
    console.log(`   Duration: ${duration.toFixed(2)} seconds`);
    console.log(`   Total Records: ${totalRecords}`);
    console.log(`   Successful: ${totalSuccess}`);
    console.log(`   Errors: ${totalErrors}`);
    console.log(`   Success Rate: ${((totalSuccess / totalRecords) * 100).toFixed(1)}%`);

    if (migration.errors.length > 0) {
      console.log('\n❌ Errors encountered:');
      migration.errors.forEach(error => console.log(`   - ${error}`));
    }

    // Save migration report
    const report = {
      mode: migration.dryRun ? 'dry-run' : 'live',
      startTime: migration.startTime,
      endTime: new Date(),
      duration: duration,
      stats: migration.stats,
      errors: migration.errors,
      mappings: migration.mappings.exportMappings()
    };

    const reportFile = path.join(process.cwd(), `migration-report-${Date.now()}.json`);
    fs.writeFileSync(reportFile, JSON.stringify(report, null, 2));
    console.log(`\n📄 Report saved: ${reportFile}`);

    if (migration.dryRun) {
      console.log('\n✅ Dry run completed successfully!');
      console.log('   Run without --dry-run flag to execute the migration.');
    } else {
      console.log('\n🎉 Migration completed successfully!');
    }

  } catch (error) {
    console.error('\n💥 Migration failed:', error.message);
    console.error(error.stack);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

// Run migration if called directly
if (require.main === module) {
  runMigration().catch(console.error);
}

module.exports = { runMigration };
