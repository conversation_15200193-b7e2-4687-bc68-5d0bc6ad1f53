#!/usr/bin/env node

/**
 * ID Mapping Strategy for Data Migration
 * Handles old ID → new ID mapping and data transformation logic
 */

const { cuid } = require('@paralleldrive/cuid2');

/**
 * ID Mapping Manager
 * Maintains mappings between old and new IDs during migration
 */
class IdMappingManager {
  constructor() {
    this.mappings = {
      cabinets: new Map(), // oldId → newId
      teachers: new Map(), // oldId → newId  
      users: new Map(),    // teacherId → userId
      courses: new Map(),  // courseKey → courseId
      groups: new Map()    // oldId → newId
    };
    
    this.reverseMap = {
      cabinets: new Map(), // newId → oldId
      teachers: new Map(), // newId → oldId
      users: new Map(),    // userId → teacherId
      courses: new Map(),  // courseId → courseKey
      groups: new Map()    // newId → oldId
    };
  }

  /**
   * Generate new ID and create mapping
   */
  createMapping(type, oldId, newId = null) {
    if (!newId) {
      newId = cuid();
    }
    
    this.mappings[type].set(oldId, newId);
    this.reverseMap[type].set(newId, oldId);
    
    return newId;
  }

  /**
   * Get new ID from old ID
   */
  getNewId(type, oldId) {
    return this.mappings[type].get(oldId);
  }

  /**
   * Get old ID from new ID
   */
  getOldId(type, newId) {
    return this.reverseMap[type].get(newId);
  }

  /**
   * Check if mapping exists
   */
  hasMapping(type, oldId) {
    return this.mappings[type].has(oldId);
  }

  /**
   * Get all mappings for a type
   */
  getAllMappings(type) {
    return Object.fromEntries(this.mappings[type]);
  }

  /**
   * Export mappings to JSON
   */
  exportMappings() {
    const exported = {};
    for (const [type, map] of Object.entries(this.mappings)) {
      exported[type] = Object.fromEntries(map);
    }
    return exported;
  }

  /**
   * Import mappings from JSON
   */
  importMappings(data) {
    for (const [type, mappings] of Object.entries(data)) {
      if (this.mappings[type]) {
        this.mappings[type] = new Map(Object.entries(mappings));
        // Rebuild reverse map
        this.reverseMap[type] = new Map();
        for (const [oldId, newId] of this.mappings[type]) {
          this.reverseMap[type].set(newId, oldId);
        }
      }
    }
  }
}

/**
 * Phone Number Normalizer
 */
class PhoneNormalizer {
  constructor() {
    this.usedNumbers = new Set();
    this.testNumberCounter = 1;
  }

  /**
   * Normalize phone number to +998XXXXXXXXX format
   */
  normalize(phone) {
    if (!phone) return null;

    // Remove all non-digit characters except +
    let cleaned = phone.replace(/[^\d+]/g, '');
    
    // Handle different formats
    if (cleaned.startsWith('+998')) {
      // Already in correct format
      return cleaned;
    } else if (cleaned.startsWith('998')) {
      // Add + prefix
      return '+' + cleaned;
    } else if (cleaned.length === 9 && cleaned.startsWith('9')) {
      // Local format: 9XXXXXXXX → +9989XXXXXXXX
      return '+998' + cleaned;
    } else if (cleaned === '123' || cleaned.length < 9) {
      // Test/placeholder number - generate unique test number
      return this.generateTestNumber();
    } else {
      // Try to format as Uzbek number
      return '+998' + cleaned.slice(-9);
    }
  }

  /**
   * Generate unique test phone number
   */
  generateTestNumber() {
    let testNumber;
    do {
      testNumber = `+99890100${String(this.testNumberCounter).padStart(4, '0')}`;
      this.testNumberCounter++;
    } while (this.usedNumbers.has(testNumber));
    
    this.usedNumbers.add(testNumber);
    return testNumber;
  }

  /**
   * Check if phone number is valid
   */
  isValid(phone) {
    const normalized = this.normalize(phone);
    return normalized && normalized.match(/^\+998\d{9}$/);
  }
}

/**
 * Email Generator
 */
class EmailGenerator {
  constructor() {
    this.usedEmails = new Set();
    this.domain = 'innovativecentre.uz';
  }

  /**
   * Generate unique email from teacher name
   */
  generateFromName(fullName) {
    if (!fullName) return null;

    // Extract first name
    const nameParts = fullName.trim().split(/\s+/);
    const firstName = nameParts[0].toLowerCase();
    
    // Transliterate Cyrillic to Latin if needed
    const transliterated = this.transliterate(firstName);
    
    // Generate base email
    let baseEmail = `teacher.${transliterated}@${this.domain}`;
    
    // Ensure uniqueness
    let email = baseEmail;
    let counter = 1;
    while (this.usedEmails.has(email)) {
      email = `teacher.${transliterated}${counter}@${this.domain}`;
      counter++;
    }
    
    this.usedEmails.add(email);
    return email;
  }

  /**
   * Simple Cyrillic to Latin transliteration
   */
  transliterate(text) {
    const map = {
      'а': 'a', 'б': 'b', 'в': 'v', 'г': 'g', 'д': 'd', 'е': 'e', 'ё': 'yo',
      'ж': 'zh', 'з': 'z', 'и': 'i', 'й': 'y', 'к': 'k', 'л': 'l', 'м': 'm',
      'н': 'n', 'о': 'o', 'п': 'p', 'р': 'r', 'с': 's', 'т': 't', 'у': 'u',
      'ф': 'f', 'х': 'h', 'ц': 'ts', 'ч': 'ch', 'ш': 'sh', 'щ': 'sch',
      'ъ': '', 'ы': 'y', 'ь': '', 'э': 'e', 'ю': 'yu', 'я': 'ya'
    };
    
    return text.toLowerCase().split('').map(char => map[char] || char).join('');
  }

  /**
   * Validate email format
   */
  isValid(email) {
    return email && email.includes('@') && email.includes('.');
  }
}

/**
 * Course Creator
 * Creates course records from class data analysis
 */
class CourseCreator {
  constructor() {
    this.courseDurations = {
      'A1': 12,
      'A2': 14, 
      'B1': 16,
      'B2': 18,
      'IELTS': 10,
      'SAT': 8,
      'Kids': 20,
      'Speaking': 6,
      'Individual': 4,
      'Math': 12
    };
  }

  /**
   * Generate course from class data
   */
  createCourse(level, subject, price) {
    const courseKey = `${level}-${subject}-${price}`;
    
    return {
      key: courseKey,
      name: this.generateCourseName(level, subject),
      level: level,
      subject: subject,
      description: this.generateDescription(level, subject),
      duration: this.courseDurations[level] || 12,
      price: price,
      isActive: true
    };
  }

  /**
   * Generate course name
   */
  generateCourseName(level, subject) {
    if (level === 'Individual') {
      return `Individual ${subject} Lessons`;
    } else if (level === 'Kids') {
      return `${subject} for Kids`;
    } else if (level === 'Speaking') {
      return `${subject} Speaking Course`;
    } else if (level === 'IELTS') {
      return `IELTS Preparation`;
    } else if (level === 'SAT') {
      return `SAT ${subject} Preparation`;
    } else {
      return `General ${subject} ${level}`;
    }
  }

  /**
   * Generate course description
   */
  generateDescription(level, subject) {
    const descriptions = {
      'A1': 'Beginner level course for students with no prior knowledge',
      'A2': 'Elementary level course building on basic foundations',
      'B1': 'Intermediate level course for developing fluency',
      'B2': 'Upper-intermediate level course for advanced communication',
      'IELTS': 'Intensive preparation for IELTS examination',
      'SAT': 'Comprehensive preparation for SAT examination',
      'Kids': 'Fun and engaging course designed specifically for children',
      'Speaking': 'Focused course to improve speaking and conversation skills',
      'Individual': 'Personalized one-on-one lessons tailored to student needs',
      'Math': 'Mathematics course covering essential concepts and problem-solving'
    };
    
    return descriptions[level] || `${level} level ${subject} course`;
  }
}

/**
 * Location Parser
 * Parses cabinet location strings to extract floor and building
 */
class LocationParser {
  /**
   * Parse location string to extract floor and building
   */
  parse(location) {
    if (!location) {
      return { floor: null, building: 'Main Building' };
    }

    // Extract floor number from various formats
    const floorMatch = location.match(/(\d+)/);
    const floor = floorMatch ? parseInt(floorMatch[1]) : null;
    
    // Determine building (default to Main Building)
    const building = 'Main Building';
    
    return { floor, building };
  }
}

/**
 * Schedule Converter
 * Converts schedule arrays to JSON strings
 */
class ScheduleConverter {
  /**
   * Convert schedule array to JSON string
   */
  convert(scheduleArray) {
    if (!scheduleArray || !Array.isArray(scheduleArray)) {
      return JSON.stringify([]);
    }

    // Validate and clean schedule data
    const cleanedSchedule = scheduleArray.map(item => ({
      day: item.day,
      startTime: item.startTime,
      endTime: item.endTime
    }));

    return JSON.stringify(cleanedSchedule);
  }

  /**
   * Validate schedule format
   */
  isValid(scheduleArray) {
    if (!Array.isArray(scheduleArray)) return false;
    
    return scheduleArray.every(item => 
      item.day && item.startTime && item.endTime
    );
  }
}

// Export classes for use in migration scripts
module.exports = {
  IdMappingManager,
  PhoneNormalizer,
  EmailGenerator,
  CourseCreator,
  LocationParser,
  ScheduleConverter
};

// Example usage if run directly
if (require.main === module) {
  console.log('🗺️  ID Mapping Strategy Components:');
  console.log('   ✓ IdMappingManager - Handles old → new ID mappings');
  console.log('   ✓ PhoneNormalizer - Normalizes phone numbers');
  console.log('   ✓ EmailGenerator - Generates unique emails');
  console.log('   ✓ CourseCreator - Creates courses from class data');
  console.log('   ✓ LocationParser - Parses cabinet locations');
  console.log('   ✓ ScheduleConverter - Converts schedule formats');
  
  // Demo phone normalization
  const phoneNormalizer = new PhoneNormalizer();
  console.log('\n📞 Phone Normalization Examples:');
  console.log('   "99-999-99-99" →', phoneNormalizer.normalize('99-999-99-99'));
  console.log('   "+99897 285 88 86" →', phoneNormalizer.normalize('+99897 285 88 86'));
  console.log('   "123" →', phoneNormalizer.normalize('123'));
  
  // Demo email generation
  const emailGenerator = new EmailGenerator();
  console.log('\n📧 Email Generation Examples:');
  console.log('   "Mukhammadxon Soliev" →', emailGenerator.generateFromName('Mukhammadxon Soliev'));
  console.log('   "Aziza Rustamova" →', emailGenerator.generateFromName('Aziza Rustamova'));
}
